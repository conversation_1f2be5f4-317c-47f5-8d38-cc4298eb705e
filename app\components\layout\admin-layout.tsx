import { Link, useLocation } from "@remix-run/react";
import {
  BarChart3,
  Bot,
  CreditCard,
  Database,
  FileText,
  Home,
  Menu,
  MessageSquare,
  Settings,
  Shield,
  ShoppingCart,
  Users,
  X,
} from "lucide-react";
import { type ReactNode, useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { cn } from "~/lib/utils/utils";

export interface AdminLayoutProps {
  children: ReactNode;
  className?: string;
}

interface NavItem {
  title: string;
  url: string;
  icon: ReactNode;
  badge?: string;
  children?: NavItem[];
}

const navigation: NavItem[] = [
  {
    title: "Dashboard",
    url: "/admin",
    icon: <BarChart3 className="h-5 w-5" />,
  },
  {
    title: "Users",
    url: "/admin/users",
    icon: <Users className="h-5 w-5" />,
    badge: "124",
  },
  {
    title: "Orders",
    url: "/admin/orders",
    icon: <ShoppingCart className="h-5 w-5" />,
    badge: "12",
  },
  {
    title: "AI Tools",
    url: "/admin/ai-tools",
    icon: <Bot className="h-5 w-5" />,
  },
  {
    title: "Content",
    url: "/admin/content",
    icon: <FileText className="h-5 w-5" />,
    children: [
      {
        title: "Blog Posts",
        url: "/admin/content/posts",
        icon: <FileText className="h-4 w-4" />,
      },
      {
        title: "Pages",
        url: "/admin/content/pages",
        icon: <FileText className="h-4 w-4" />,
      },
    ],
  },
  {
    title: "Payments",
    url: "/admin/payments",
    icon: <CreditCard className="h-5 w-5" />,
  },
  {
    title: "Feedback",
    url: "/admin/feedback",
    icon: <MessageSquare className="h-5 w-5" />,
    badge: "3",
  },
  {
    title: "Database",
    url: "/admin/database",
    icon: <Database className="h-5 w-5" />,
  },
  {
    title: "Security",
    url: "/admin/security",
    icon: <Shield className="h-5 w-5" />,
  },
  {
    title: "Settings",
    url: "/admin/settings",
    icon: <Settings className="h-5 w-5" />,
  },
];

function NavLink({ item, isActive }: { item: NavItem; isActive: boolean }) {
  return (
    <Link
      to={item.url}
      className={cn(
        "flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors",
        isActive
          ? "bg-primary text-primary-foreground"
          : "text-muted-foreground hover:text-foreground hover:bg-muted"
      )}
    >
      {item.icon}
      <span className="flex-1">{item.title}</span>
      {item.badge && (
        <Badge variant={isActive ? "secondary" : "outline"} className="text-xs">
          {item.badge}
        </Badge>
      )}
    </Link>
  );
}

export default function AdminLayout({ children, className = "" }: AdminLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const location = useLocation();

  const isActive = (url: string) => {
    if (url === "/admin") {
      return location.pathname === "/admin";
    }
    return location.pathname.startsWith(url);
  };

  return (
    <div className={`min-h-screen bg-background ${className}`}>
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <aside
        className={cn(
          "fixed inset-y-0 left-0 z-50 w-64 bg-card border-r transform transition-transform duration-200 ease-in-out lg:translate-x-0",
          sidebarOpen ? "translate-x-0" : "-translate-x-full"
        )}
      >
        <div className="flex h-full flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b">
            <Link to="/admin" className="flex items-center gap-2">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <BarChart3 className="h-5 w-5 text-primary-foreground" />
              </div>
              <span className="font-bold text-lg">Admin</span>
            </Link>
            <Button
              variant="ghost"
              size="icon"
              className="lg:hidden"
              onClick={() => setSidebarOpen(false)}
            >
              <X className="h-5 w-5" />
            </Button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
            {navigation.map((item) => (
              <div key={item.url}>
                <NavLink item={item} isActive={isActive(item.url)} />
                {item.children && isActive(item.url) && (
                  <div className="ml-6 mt-2 space-y-1">
                    {item.children.map((child) => (
                      <NavLink key={child.url} item={child} isActive={isActive(child.url)} />
                    ))}
                  </div>
                )}
              </div>
            ))}
          </nav>

          {/* User Profile */}
          <div className="p-4 border-t">
            <div className="flex items-center gap-3">
              <Avatar>
                <AvatarImage src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face" />
                <AvatarFallback>AD</AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">Admin User</p>
                <p className="text-xs text-muted-foreground truncate"><EMAIL></p>
              </div>
            </div>
          </div>
        </div>
      </aside>

      {/* Main content */}
      <div className="lg:ml-64">
        {/* Top bar */}
        <header className="sticky top-0 z-30 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b">
          <div className="flex items-center justify-between px-4 py-3">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="icon"
                className="lg:hidden"
                onClick={() => setSidebarOpen(true)}
              >
                <Menu className="h-5 w-5" />
              </Button>

              {/* Breadcrumb */}
              <nav className="flex items-center gap-2 text-sm">
                <Link to="/" className="text-muted-foreground hover:text-foreground">
                  <Home className="h-4 w-4" />
                </Link>
                <span className="text-muted-foreground">/</span>
                <span className="font-medium">Admin</span>
                {location.pathname !== "/admin" && (
                  <>
                    <span className="text-muted-foreground">/</span>
                    <span className="text-muted-foreground">
                      {location.pathname.split("/").pop()?.replace("-", " ")}
                    </span>
                  </>
                )}
              </nav>
            </div>

            <div className="flex items-center gap-4">
              <Button variant="outline" size="sm" asChild>
                <Link to="/">View Site</Link>
              </Button>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="p-6">{children}</main>
      </div>
    </div>
  );
}
