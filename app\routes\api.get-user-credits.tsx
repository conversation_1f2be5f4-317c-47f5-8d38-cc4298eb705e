import type { ActionFunctionArgs } from "@remix-run/cloudflare";
import { respData, respErr } from "~/lib/api/resp";
import { createDbFromEnv } from "~/lib/db";
import { getUserCreditBalance } from "~/models/user";
import { getUserUuid } from "~/services/user";

export async function action({ request, context }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return respErr("Method not allowed");
  }

  try {
    // Create database connection with enhanced error handling
    const db = context.db || createDbFromEnv(context.cloudflare?.env);
    if (!db) {
      return respErr("Database connection failed");
    }

    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("Authentication required");
    }

    // Use the new enhanced credit balance function
    const credits = await getUserCreditBalance(user_uuid, db);

    return respData({
      credits,
      userUuid: user_uuid,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Get user credits failed:", error);
    return respErr(error instanceof Error ? error.message : "Failed to get user credits");
  }
}
