/**
 * Auth Me API Route
 * Returns current user information if authenticated
 */

import type { LoaderFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { getUser } from "~/lib/auth/middleware.server";

export async function loader({ request, context }: LoaderFunctionArgs) {
  try {
    const userResult = await getUser(request, context.cloudflare?.env);

    if (userResult.success) {
      return json({
        success: true,
        user: {
          id: userResult.user.id,
          uuid: userResult.user.uuid,
          email: userResult.user.email,
          name: userResult.user.name,
          avatar: userResult.user.avatar,
          credits: userResult.user.credits,
          createdAt: userResult.user.createdAt,
          sessionId: userResult.user.sessionId,
        },
      });
    } else {
      return json(
        {
          success: false,
          error: userResult.error,
        },
        { status: 401 }
      );
    }
  } catch (error) {
    console.error("Auth me API error:", error);
    return json(
      {
        success: false,
        error: "Internal server error",
      },
      { status: 500 }
    );
  }
}
