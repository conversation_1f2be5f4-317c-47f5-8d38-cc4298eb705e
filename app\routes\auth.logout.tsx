/**
 * Logout Route
 * Handles user logout and session cleanup
 */

import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/cloudflare";
import { redirect } from "@remix-run/cloudflare";
import { clearAuthCookies } from "~/lib/auth/jwt.server";
import { getUser, logoutUser } from "~/lib/auth/middleware.server";

export async function action({ request, context }: ActionFunctionArgs) {
  try {
    // Get current user to get session ID
    const userResult = await getUser(request, context.cloudflare?.env);

    if (userResult.success) {
      // Deactivate the session
      await logoutUser(userResult.user.sessionId, context.cloudflare?.env);
    }

    // Clear authentication cookies
    const headers = clearAuthCookies();

    // Redirect to home page
    return redirect("/", {
      headers,
    });
  } catch (error) {
    console.error("Logout error:", error);

    // Even if there's an error, clear cookies and redirect
    const headers = clearAuthCookies();
    return redirect("/", {
      headers,
    });
  }
}

export async function loader({ request, context }: LoaderFunctionArgs) {
  // Handle GET requests the same way as POST
  try {
    const userResult = await getUser(request, context.cloudflare?.env);

    if (userResult.success) {
      await logoutUser(userResult.user.sessionId, context.cloudflare?.env);
    }

    const headers = clearAuthCookies();
    return redirect("/", {
      headers,
    });
  } catch (error) {
    console.error("Logout error:", error);

    const headers = clearAuthCookies();
    return redirect("/", {
      headers,
    });
  }
}
