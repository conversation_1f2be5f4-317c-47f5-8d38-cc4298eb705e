# Copy this file to .dev.vars and fill in your actual values
# DO NOT commit .dev.vars to version control

# Database Configuration
# Neon database connection string format:
# ****************************************************************
DATABASE_URL="************************************************************************************"

# AI Provider API Keys
# Get these from the respective provider websites

# OpenAI (https://platform.openai.com/api-keys)
OPENAI_API_KEY="sk-your-openai-api-key-here"

# DeepSeek (https://platform.deepseek.com/)
DEEPSEEK_API_KEY="your-deepseek-api-key-here"

# OpenRouter (https://openrouter.ai/keys)
OPENROUTER_API_KEY="sk-or-your-openrouter-key-here"

# SiliconFlow (https://siliconflow.cn/)
SILICONFLOW_API_KEY="your-siliconflow-api-key-here"
SILICONFLOW_BASE_URL="https://api.siliconflow.cn/v1"

# Replicate (https://replicate.com/account/api-tokens)
REPLICATE_API_TOKEN="r8_your-replicate-token-here"

# Payment Processing
STRIPE_PUBLIC_KEY="your-stripe-public-key"
STRIPE_SECRET_KEY="your-stripe-secret-key"

# Analytics
GA_TRACKING_ID="G-XXXXXXXXXX"

# Authentication
GOOGLE_CLIENT_ID="your-google-client-id.apps.googleusercontent.com"
ONE_TAP_ENABLED="true"
JWT_SECRET="your-super-secret-jwt-key-change-in-production-min-32-chars"
