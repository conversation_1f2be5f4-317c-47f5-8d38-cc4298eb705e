import type { ActionFunctionArgs } from "@remix-run/cloudflare";
import type <PERSON><PERSON> from "stripe";
import { respOk, respServerErr } from "~/lib/api/resp";
import { getStripeClient } from "~/lib/payment/stripe.server";
import { handleOrderSession } from "~/services/order";

export async function action({ request, context }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return respServerErr("Method not allowed");
  }

  try {
    const db = context.db;
    if (!db) {
      return respServerErr("Database not available");
    }

    const stripeSecretKey = context.cloudflare.env.STRIPE_SECRET_KEY;
    const stripeWebhookSecret = context.cloudflare.env.STRIPE_WEBHOOK_SECRET;

    if (!stripeSecretKey || !stripeWebhookSecret) {
      throw new Error("invalid stripe config");
    }

    const stripe = getStripeClient(stripeSecretKey);

    const signature = request.headers.get("stripe-signature");
    const body = await request.text();

    if (!signature || !body) {
      throw new Error("invalid notify data");
    }

    const event = stripe.webhooks.constructEvent(body, signature, stripeWebhookSecret);

    console.log("stripe notify event: ", event.type);

    switch (event.type) {
      case "checkout.session.completed": {
        const session = event.data.object as Stripe.Checkout.Session;
        await handleOrderSession(session, db);
        break;
      }

      default:
        console.log("not handled event: ", event.type);
    }

    return respOk();
  } catch (e: any) {
    console.log("stripe notify failed: ", e);
    return respServerErr(`handle stripe notify failed: ${e.message}`);
  }
}
