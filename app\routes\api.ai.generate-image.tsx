// import { openai } from "@ai-sdk/openai"; // Removed - using direct API calls
import { replicate } from "@ai-sdk/replicate";
import type { ActionFunctionArgs } from "@remix-run/cloudflare";
import { generateObject } from "ai";
import { z } from "zod";
import {
  AI_ERROR_MESSAGES,
  getAIEnvironmentVariables,
  getAIErrorMessage,
  logAIOperation,
  sanitizePromptForLogging,
} from "~/lib/ai/ai-utils";
import { respData, respErr } from "~/lib/api/resp";
import { CreditsAmount, CreditsTransType, decreaseCredits } from "~/services/credit";
import { getUserUuid } from "~/services/user";

/**
 * Image generation providers
 */
export type ImageProvider = "openai" | "replicate";

/**
 * Image generation request schema
 */
const ImageGenerationSchema = z.object({
  prompt: z.string().min(1, "Prompt is required"),
  provider: z.enum(["openai", "replicate"]),
  model: z.string().optional(),
  size: z.enum(["256x256", "512x512", "1024x1024", "1792x1024", "1024x1792"]).optional(),
  quality: z.enum(["standard", "hd"]).optional(),
  style: z.enum(["vivid", "natural"]).optional(),
  n: z.number().min(1).max(4).optional(),
});

/**
 * Image generation models configuration
 */
const IMAGE_MODELS = {
  openai: {
    "dall-e-2": {
      sizes: ["256x256", "512x512", "1024x1024"],
      maxImages: 10,
    },
    "dall-e-3": {
      sizes: ["1024x1024", "1792x1024", "1024x1792"],
      maxImages: 1,
      supportsQuality: true,
      supportsStyle: true,
    },
  },
  replicate: {
    "stability-ai/stable-diffusion": {
      sizes: ["512x512", "1024x1024"],
      maxImages: 4,
    },
  },
} as const;

export async function action({ request, context }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return respErr("Method not allowed");
  }

  const startTime = Date.now();
  let provider: ImageProvider | undefined;
  let model: string | undefined;

  try {
    const db = context.db;
    if (!db) {
      return respErr("Database not available");
    }
    // Parse and validate request body
    const body = await request.json();
    const validationResult = ImageGenerationSchema.safeParse(body);

    if (!validationResult.success) {
      return respErr(
        `Invalid parameters: ${validationResult.error.errors.map((e) => e.message).join(", ")}`
      );
    }

    const {
      prompt,
      provider: reqProvider,
      model: reqModel = getDefaultModel(reqProvider),
      size = "1024x1024",
      quality = "standard",
      style = "vivid",
      n = 1,
    } = validationResult.data;

    provider = reqProvider;
    model = reqModel;

    // Check user authentication
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("Authentication required");
    }

    // Log the operation start
    console.log("Starting AI image generation:", {
      provider,
      model,
      prompt: sanitizePromptForLogging(prompt),
      size,
      quality,
      style,
      n,
      user_uuid,
      timestamp: new Date().toISOString(),
    });

    // Get environment variables
    const env = getAIEnvironmentVariables(context);

    // Generate image based on provider
    let imageUrls: string[] = [];

    if (provider === "openai") {
      imageUrls = await generateWithOpenAI({
        prompt,
        model,
        size,
        quality,
        style,
        n,
        env,
      });
    } else if (provider === "replicate") {
      imageUrls = await generateWithReplicate({
        prompt,
        model,
        size,
        n,
        env,
      });
    } else {
      return respErr("Unsupported image generation provider");
    }

    // Decrease user credits after successful generation
    try {
      const creditCost = CreditsAmount.AIImageGenerationCost * n;
      await decreaseCredits(
        {
          user_uuid,
          trans_type: CreditsTransType.AIImageGeneration,
          credits: creditCost,
        },
        db
      );
    } catch (creditError) {
      console.error("Failed to decrease credits:", creditError);
      // Continue with the response even if credit deduction fails
    }

    // Log successful operation
    logAIOperation("generate-image", provider, model, true, Date.now() - startTime);

    // Return successful response
    return respData({
      images: imageUrls,
      provider,
      model,
      parameters: {
        size,
        quality,
        style,
        n,
      },
    });
  } catch (error) {
    // Log failed operation
    if (provider && model) {
      logAIOperation("generate-image", provider, model, false, Date.now() - startTime, error);
    }

    console.error("AI image generation failed:", {
      error: error.message || error,
      provider,
      model,
      stack: error.stack,
    });

    // Return user-friendly error message
    const errorMessage = getAIErrorMessage(error);
    return respErr(errorMessage);
  }
}

/**
 * Generate image with OpenAI DALL-E
 */
async function generateWithOpenAI({
  prompt,
  model,
  size,
  quality,
  style,
  n,
  env,
}: {
  prompt: string;
  model: string;
  size: string;
  quality: string;
  style: string;
  n: number;
  env: any;
}): Promise<string[]> {
  if (!env.OPENAI_API_KEY) {
    throw new Error("OPENAI_API_KEY is required for OpenAI image generation");
  }

  // OpenAI client setup - we'll use direct API calls for image generation

  // Use OpenAI's image generation API directly
  const response = await fetch("https://api.openai.com/v1/images/generations", {
    method: "POST",
    headers: {
      Authorization: `Bearer ${env.OPENAI_API_KEY}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      model,
      prompt,
      size,
      quality: model === "dall-e-3" ? quality : undefined,
      style: model === "dall-e-3" ? style : undefined,
      n: model === "dall-e-3" ? 1 : n, // DALL-E 3 only supports n=1
      response_format: "url",
    }),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error?.message || "OpenAI image generation failed");
  }

  const result = await response.json();
  return result.data.map((item: any) => item.url);
}

/**
 * Generate image with Replicate
 */
async function generateWithReplicate({
  prompt,
  model,
  size,
  n,
  env,
}: {
  prompt: string;
  model: string;
  size: string;
  n: number;
  env: any;
}): Promise<string[]> {
  if (!env.REPLICATE_API_TOKEN) {
    throw new Error("REPLICATE_API_TOKEN is required for Replicate image generation");
  }

  // This is a simplified implementation
  // In a real scenario, you'd use the Replicate API directly
  throw new Error("Replicate image generation not yet implemented");
}

/**
 * Get default model for provider
 */
function getDefaultModel(provider: ImageProvider): string {
  switch (provider) {
    case "openai":
      return "dall-e-3";
    case "replicate":
      return "stability-ai/stable-diffusion";
    default:
      throw new Error(`Unknown provider: ${provider}`);
  }
}
