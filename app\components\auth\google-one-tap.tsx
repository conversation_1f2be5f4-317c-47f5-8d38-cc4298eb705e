/**
 * Google One Tap Login Component
 * Handles Google One Tap authentication flow
 */

import { useNavigate } from "@remix-run/react";
import { useEffect, useRef, useState } from "react";
import type { GoogleNotification, GoogleRenderOptions } from "~/types/common";

// Google One Tap types
declare global {
  interface Window {
    google?: {
      accounts: {
        id: {
          initialize: (config: GoogleOneTapConfig) => void;
          prompt: (callback?: (notification: GoogleNotification) => void) => void;
          renderButton: (parent: HTMLElement, options: GoogleRenderOptions) => void;
          disableAutoSelect: () => void;
        };
      };
    };
  }
}

interface GoogleOneTapConfig {
  client_id: string;
  callback: (response: { credential: string }) => void;
  auto_select?: boolean;
  cancel_on_tap_outside?: boolean;
  prompt_parent_id?: string;
  ux_mode?: "popup" | "redirect";
  context?: "signin" | "signup" | "use";
}

interface GoogleOneTapLoginProps {
  clientId: string;
  oneTapEnabled: boolean;
  promptParentId?: string;
  autoSelect?: boolean;
  context?: "signin" | "signup" | "use";
}

export function GoogleOneTapLogin({
  clientId,
  oneTapEnabled,
  promptParentId,
  autoSelect = false,
  context = "signin",
}: GoogleOneTapLoginProps) {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isScriptLoaded, setIsScriptLoaded] = useState(false);
  const buttonRef = useRef<HTMLDivElement>(null);
  const oneTapInitialized = useRef(false);

  // Load Google One Tap script
  useEffect(() => {
    if (!oneTapEnabled || !clientId) return;

    const script = document.createElement("script");
    script.src = "https://accounts.google.com/gsi/client";
    script.async = true;
    script.defer = true;
    script.onload = () => setIsScriptLoaded(true);
    script.onerror = () => setError("Failed to load Google authentication");

    document.head.appendChild(script);

    return () => {
      // Cleanup script
      const existingScript = document.querySelector(
        'script[src="https://accounts.google.com/gsi/client"]'
      );
      if (existingScript) {
        document.head.removeChild(existingScript);
      }
    };
  }, [oneTapEnabled, clientId]);

  // Handle credential response
  const handleCredentialResponse = async (response: { credential: string }) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await fetch("/auth/google", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          credential: response.credential,
        }),
      });

      const data = await result.json();

      if (data.success) {
        // Authentication successful, redirect to console
        navigate("/console", { replace: true });
      } else {
        setError(data.error || "Authentication failed");
      }
    } catch (error) {
      console.error("Authentication error:", error);
      setError("Network error. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  // Initialize Google One Tap
  useEffect(() => {
    if (!isScriptLoaded || !window.google || oneTapInitialized.current) return;

    try {
      // Initialize Google One Tap
      window.google.accounts.id.initialize({
        client_id: clientId,
        callback: handleCredentialResponse,
        auto_select: autoSelect,
        cancel_on_tap_outside: true,
        prompt_parent_id: promptParentId,
        ux_mode: "popup",
        context,
      });

      // Show One Tap prompt
      window.google.accounts.id.prompt((notification) => {
        if (notification.isNotDisplayed()) {
          console.log("One Tap not displayed:", notification.getNotDisplayedReason());
        } else if (notification.isSkippedMoment()) {
          console.log("One Tap skipped:", notification.getSkippedReason());
        } else if (notification.isDismissedMoment()) {
          console.log("One Tap dismissed:", notification.getDismissedReason());
        }
      });

      // Render sign-in button as fallback
      if (buttonRef.current) {
        window.google.accounts.id.renderButton(buttonRef.current, {
          theme: "outline",
          size: "large",
          width: "100%",
          text: "signin_with",
          shape: "rectangular",
        });
      }

      oneTapInitialized.current = true;
    } catch (error) {
      console.error("Google One Tap initialization error:", error);
      setError("Failed to initialize Google authentication");
    }
  }, [isScriptLoaded, clientId, autoSelect, promptParentId, context]);

  if (!oneTapEnabled || !clientId) {
    return <div className="text-center text-gray-500">Google authentication is not enabled</div>;
  }

  return (
    <div className="space-y-4">
      {/* Loading state */}
      {isLoading && (
        <div className="text-center">
          <div className="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-white bg-blue-500 hover:bg-blue-400 transition ease-in-out duration-150 cursor-not-allowed">
            <svg
              className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              />
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              />
            </svg>
            Signing in...
          </div>
        </div>
      )}

      {/* Error message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Authentication Error</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Google Sign-In Button */}
      {!isLoading && (
        <div className="space-y-4">
          <div className="text-center text-sm text-gray-600">
            Click the button below or use the One Tap prompt above
          </div>
          <div ref={buttonRef} className="flex justify-center" />
        </div>
      )}

      {/* Instructions */}
      <div className="text-center text-xs text-gray-500">
        <p>If the One Tap prompt doesn't appear, you can use the button above to sign in.</p>
      </div>
    </div>
  );
}
