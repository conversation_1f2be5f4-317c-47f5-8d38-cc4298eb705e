import { Link } from "@remix-run/react";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import HeroBg from "./bg";
import HappyUsers from "./happy-users";

export interface HeroButton {
  title: string;
  url: string;
  variant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive";
  target?: string;
  icon?: string;
}

export interface HeroAnnouncement {
  title: string;
  url: string;
  label?: string;
}

export interface HeroProps {
  title: string;
  description?: string;
  highlight_text?: string;
  buttons?: HeroButton[];
  announcement?: HeroAnnouncement;
  tip?: string;
  show_badge?: boolean;
  show_happy_users?: boolean;
  disabled?: boolean;
}

export default function Hero({
  title,
  description,
  highlight_text,
  buttons,
  announcement,
  tip,
  show_badge = false,
  show_happy_users = false,
  disabled = false,
}: HeroProps) {
  if (disabled) {
    return null;
  }

  // Split title by highlight text for gradient effect
  let texts = null;
  if (highlight_text) {
    texts = title?.split(highlight_text, 2);
  }

  return (
    <>
      <HeroBg />
      <section className="py-40 relative overflow-hidden">
        {/* Floating elements for visual interest */}
        <div className="absolute inset-0 pointer-events-none">
          <div className="absolute top-20 left-10 w-20 h-20 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-full blur-xl animate-pulse" />
          <div className="absolute top-40 right-20 w-32 h-32 bg-gradient-to-r from-purple-500/15 to-pink-500/15 rounded-full blur-2xl animate-pulse delay-1000" />
          <div className="absolute bottom-40 left-1/4 w-24 h-24 bg-gradient-to-r from-cyan-500/20 to-blue-500/20 rounded-full blur-xl animate-pulse delay-2000" />

          {/* Floating icons */}
          <div className="absolute top-32 right-1/4 text-blue-500/30 animate-bounce delay-500">
            <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2L2 7v10c0 5.55 3.84 9.739 9 11 5.16-1.261 9-5.45 9-11V7l-10-5z" />
            </svg>
          </div>
          <div className="absolute bottom-32 right-10 text-purple-500/30 animate-bounce delay-1000">
            <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
              <path d="M13 3L4 14h7v7l9-11h-7V3z" />
            </svg>
          </div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          {show_badge && (
            <div className="flex items-center justify-center mb-12 animate-fade-in">
              <div className="group relative">
                <div className="absolute -inset-1 bg-gradient-to-r from-blue-600 via-purple-600 to-cyan-600 rounded-full blur opacity-25 group-hover:opacity-40 transition duration-1000 group-hover:duration-200" />
                <div className="relative h-14 px-8 py-4 bg-white dark:bg-gray-900 border border-blue-500/20 rounded-full shadow-2xl hover:shadow-blue-500/25 transition-all duration-300 hover:scale-105 flex items-center gap-3">
                  <div className="w-3 h-3 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full animate-pulse" />
                  <span className="text-sm font-semibold bg-gradient-to-r from-blue-600 via-purple-600 to-cyan-600 bg-clip-text text-transparent">
                    🚀 AI-Powered SaaS Platform - Now Live!
                  </span>
                  <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full animate-ping" />
                </div>
              </div>
            </div>
          )}

          <div className="text-center space-y-8">
            {announcement && (
              <Link
                to={announcement.url}
                className="mx-auto mb-6 inline-flex items-center gap-3 rounded-full border border-blue-200/50 dark:border-blue-700/50 bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm px-4 py-2 text-sm hover:bg-blue-50/80 dark:hover:bg-blue-900/50 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl animate-fade-in-up"
              >
                {announcement.label && (
                  <Badge
                    variant="secondary"
                    className="bg-gradient-to-r from-blue-500 to-purple-500 text-white border-0"
                  >
                    {announcement.label}
                  </Badge>
                )}
                <span className="font-medium">{announcement.title}</span>
                <svg
                  className="w-4 h-4 transition-transform group-hover:translate-x-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </Link>
            )}

            {texts && texts.length > 1 ? (
              <h1 className="mx-auto mb-8 mt-6 max-w-7xl text-balance text-6xl font-extrabold lg:mb-12 lg:text-9xl leading-[0.9] animate-fade-in-up delay-200 tracking-tight">
                <span className="block text-gray-900 dark:text-white">{texts[0]}</span>
                <span className="block relative">
                  <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-cyan-600 bg-clip-text text-transparent animate-gradient-x">
                    {highlight_text}
                  </span>
                  <div className="absolute -inset-2 bg-gradient-to-r from-blue-600/20 via-purple-600/20 to-cyan-600/20 blur-2xl -z-10 animate-pulse" />
                </span>
                <span className="block text-gray-900 dark:text-white">{texts[1]}</span>
              </h1>
            ) : (
              <h1 className="mx-auto mb-8 mt-6 max-w-7xl text-balance text-6xl font-extrabold lg:mb-12 lg:text-9xl leading-[0.9] animate-fade-in-up delay-200 tracking-tight text-gray-900 dark:text-white">
                {title}
              </h1>
            )}

            {description && (
              <p className="mx-auto max-w-4xl text-xl text-gray-600 dark:text-gray-300 lg:text-2xl leading-relaxed animate-fade-in-up delay-400 font-medium">
                {description}
              </p>
            )}

            {buttons && buttons.length > 0 && (
              <div className="mt-12 flex flex-col justify-center gap-6 sm:flex-row animate-fade-in-up delay-600">
                {buttons.map((button, i) => (
                  <Link
                    key={i}
                    to={button.url}
                    target={button.target || ""}
                    className="group relative"
                  >
                    {i === 0 && (
                      <div className="absolute -inset-1 bg-gradient-to-r from-blue-600 via-purple-600 to-cyan-600 rounded-2xl blur opacity-30 group-hover:opacity-50 transition duration-1000 group-hover:duration-200" />
                    )}
                    <Button
                      className={`
                        relative px-10 py-6 text-lg font-bold transition-all duration-300 transform hover:scale-105 hover:shadow-2xl rounded-2xl
                        ${
                          i === 0
                            ? "bg-gradient-to-r from-blue-600 via-purple-600 to-cyan-600 hover:from-blue-700 hover:via-purple-700 hover:to-cyan-700 text-white border-0 shadow-2xl hover:shadow-blue-500/40"
                            : "border-2 border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 shadow-xl hover:shadow-2xl"
                        }
                      `}
                      size="lg"
                      variant={button.variant || "default"}
                    >
                      <div className="flex items-center gap-3">
                        {button.icon && (
                          <span className="text-xl group-hover:scale-110 transition-transform">
                            {button.icon}
                          </span>
                        )}
                        <span>{button.title}</span>
                        {i === 0 && (
                          <div className="flex items-center gap-1">
                            <div className="w-1 h-1 bg-white/60 rounded-full animate-pulse" />
                            <svg
                              className="w-5 h-5 transition-transform group-hover:translate-x-1"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M13 7l5 5m0 0l-5 5m5-5H6"
                              />
                            </svg>
                          </div>
                        )}
                      </div>
                      {i === 0 && (
                        <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      )}
                    </Button>
                  </Link>
                ))}
              </div>
            )}

            {tip && (
              <p className="mt-8 text-sm text-muted-foreground/80 animate-fade-in-up delay-800 flex items-center justify-center gap-2">
                <svg
                  className="w-4 h-4 text-green-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
                {tip}
              </p>
            )}

            {show_happy_users && <HappyUsers />}
          </div>
        </div>
      </section>
    </>
  );
}
