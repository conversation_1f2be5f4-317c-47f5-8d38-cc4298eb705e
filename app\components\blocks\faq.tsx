import { useState } from "react";
import { Card, CardContent } from "~/components/ui/card";
import ContentSection from "./content-section";

interface FAQItem {
  question: string;
  answer: string;
}

interface FAQProps {
  title: string;
  description: string;
  items: FAQItem[];
}

export default function FAQ({ title, description, items }: FAQProps) {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <ContentSection
      title={title}
      description={description}
      background="default"
      decorations={true}
      padding="lg"
      headerSpacing="md"
      maxWidth="4xl"
    >
      {/* Enhanced FAQ Items */}
      <div className="space-y-6">
        {items.map((item, index) => (
          <Card
            key={index}
            className="group border-0 bg-white/50 dark:bg-gray-900/50 backdrop-blur-sm hover:bg-white/80 dark:hover:bg-gray-900/80 transition-all duration-300 hover:scale-[1.02] hover:shadow-xl"
          >
            <CardContent className="p-0">
              <button
                type="button"
                onClick={() => toggleFAQ(index)}
                className="w-full text-left p-6 focus:outline-none focus:ring-2 focus:ring-blue-500/20 rounded-lg"
              >
                <div className="flex items-center justify-between">
                  <h3 className="text-xl font-semibold pr-4 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                    {item.question}
                  </h3>
                  <div
                    className={`w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white text-lg transition-transform duration-300 ${
                      openIndex === index ? "rotate-45" : ""
                    }`}
                  >
                    +
                  </div>
                </div>
              </button>

              {openIndex === index && (
                <div className="px-6 pb-6 pt-0">
                  <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                    <p className="text-gray-600 dark:text-gray-300 text-base leading-relaxed">
                      {item.answer}
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </ContentSection>
  );
}
