/**
 * Authentication Store using Zustand
 * Manages user authentication state across the application
 */

import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

// User interface
export interface User {
  id: string;
  uuid: string;
  email: string;
  name: string;
  avatar?: string;
  credits: number;
  createdAt: Date;
  sessionId: string;
}

// Authentication state interface
interface AuthState {
  // State
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;

  // Actions
  setUser: (user: User | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  login: (user: User) => void;
  logout: () => void;
  updateUser: (updates: Partial<User>) => void;
  updateCredits: (credits: number) => void;
  clearError: () => void;

  // Async actions
  checkAuth: () => Promise<void>;
  refreshAuth: () => Promise<void>;
}

// Create the auth store
export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Sync actions
      setUser: (user) => {
        set({
          user,
          isAuthenticated: !!user,
          error: null,
        });
      },

      setLoading: (isLoading) => {
        set({ isLoading });
      },

      setError: (error) => {
        set({ error });
      },

      login: (user) => {
        set({
          user,
          isAuthenticated: true,
          error: null,
        });
      },

      logout: () => {
        set({
          user: null,
          isAuthenticated: false,
          error: null,
        });
      },

      updateUser: (updates) => {
        const currentUser = get().user;
        if (currentUser) {
          set({
            user: { ...currentUser, ...updates },
          });
        }
      },

      updateCredits: (credits) => {
        const currentUser = get().user;
        if (currentUser) {
          set({
            user: { ...currentUser, credits },
          });
        }
      },

      clearError: () => {
        set({ error: null });
      },

      // Async actions
      checkAuth: async () => {
        const { setLoading, setUser, setError } = get();

        setLoading(true);
        setError(null);

        try {
          const response = await fetch("/api/auth/me", {
            method: "GET",
            credentials: "include",
          });

          if (response.ok) {
            const data = await response.json();
            if (data.success && data.user) {
              setUser(data.user);
            } else {
              setUser(null);
            }
          } else {
            setUser(null);
          }
        } catch (error) {
          console.error("Auth check failed:", error);
          setError("Failed to check authentication status");
          setUser(null);
        } finally {
          setLoading(false);
        }
      },

      refreshAuth: async () => {
        const { setError, setUser } = get();

        try {
          const response = await fetch("/api/auth/refresh", {
            method: "POST",
            credentials: "include",
          });

          if (response.ok) {
            const data = await response.json();
            if (data.success && data.user) {
              setUser(data.user);
              return;
            }
          }

          // If refresh fails, logout
          setUser(null);
        } catch (error) {
          console.error("Auth refresh failed:", error);
          setError("Session expired. Please sign in again.");
          setUser(null);
        }
      },
    }),
    {
      name: "auth-storage",
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// Selectors for easier access
export const useUser = () => useAuthStore((state) => state.user);
export const useIsAuthenticated = () => useAuthStore((state) => state.isAuthenticated);
export const useAuthLoading = () => useAuthStore((state) => state.isLoading);
export const useAuthError = () => useAuthStore((state) => state.error);

// Auth actions
export const useAuthActions = () => {
  const store = useAuthStore();
  return {
    login: store.login,
    logout: store.logout,
    updateUser: store.updateUser,
    updateCredits: store.updateCredits,
    setError: store.setError,
    clearError: store.clearError,
    checkAuth: store.checkAuth,
    refreshAuth: store.refreshAuth,
  };
};
