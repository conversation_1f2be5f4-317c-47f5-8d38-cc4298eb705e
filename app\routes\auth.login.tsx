/**
 * Sign In Page
 * Google One Tap authentication interface
 */

import type { LoaderFunctionArgs, MetaFunction } from "@remix-run/cloudflare";
import { json, redirect } from "@remix-run/cloudflare";
import { Link, useLoaderData } from "@remix-run/react";
import { AlertTriangle } from "lucide-react";
import { GoogleOneTapLogin } from "~/components/auth/google-one-tap";
import UnifiedLayout from "~/components/layout/unified-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { getUser } from "~/lib/auth/middleware.server";

export const meta: MetaFunction = () => {
  return [
    { title: "Sign In - AI SaaS Starter" },
    { name: "description", content: "Sign in to your account" },
  ];
};

export async function loader({ request, context }: LoaderFunctionArgs) {
  // Check if user is already authenticated
  const userResult = await getUser(request, context.cloudflare?.env);

  if (userResult.success) {
    // User is already authenticated, redirect to dashboard
    return redirect("/console");
  }

  // Get environment variables
  const googleClientId = context.cloudflare?.env?.GOOGLE_CLIENT_ID;
  const oneTapEnabled = context.cloudflare?.env?.ONE_TAP_ENABLED === "true";

  return json({
    googleClientId,
    oneTapEnabled,
  });
}

export default function SignInPage() {
  const { googleClientId, oneTapEnabled } = useLoaderData<typeof loader>();

  return (
    <UnifiedLayout
      hero={{
        title: "Welcome Back!",
        description: "Sign in to access your dashboard and services.",
      }}
    >
      <section className="py-16">
        <div className="max-w-md mx-auto">
          <Card className="shadow-2xl border-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
            <CardHeader className="text-center">
              <CardTitle className="text-2xl font-bold">Sign In</CardTitle>
              <CardDescription>Access your AI workspace with Google authentication</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {googleClientId && oneTapEnabled ? (
                <GoogleOneTapLogin clientId={googleClientId} oneTapEnabled={oneTapEnabled} />
              ) : (
                <Card className="border-yellow-200 bg-yellow-50 dark:bg-yellow-900/20">
                  <CardContent className="pt-6">
                    <div className="flex items-start gap-3">
                      <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                      <div>
                        <h3 className="font-medium text-yellow-800 dark:text-yellow-200">
                          Authentication Not Configured
                        </h3>
                        <p className="mt-1 text-sm text-yellow-700 dark:text-yellow-300">
                          Google authentication is not properly configured. Please check your
                          environment variables.
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              <div className="text-center text-xs text-gray-500 dark:text-gray-400">
                By signing in, you agree to our{" "}
                <Link
                  to="/terms-of-service"
                  className="text-blue-600 hover:text-blue-500 dark:text-blue-400"
                >
                  Terms of Service
                </Link>{" "}
                and{" "}
                <Link
                  to="/privacy-policy"
                  className="text-blue-600 hover:text-blue-500 dark:text-blue-400"
                >
                  Privacy Policy
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    </UnifiedLayout>
  );
}
