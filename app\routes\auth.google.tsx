/**
 * Google Authentication Route
 * Handles Google One Tap ID Token verification and user authentication
 */

import type { ActionFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { handleGoogleAuth } from "~/lib/auth/google.server";

// Type definitions for request body
interface GoogleAuthBody {
  credential: string;
  inviteCode?: string;
}
import { setAuthCookies } from "~/lib/auth/jwt.server";

export async function action({ request, context }: ActionFunctionArgs) {
  // Only allow POST requests
  if (request.method !== "POST") {
    return json({ success: false, error: "Method not allowed" }, { status: 405 });
  }

  try {
    // Parse request body
    const body = (await request.json()) as GoogleAuthBody;
    const { credential, inviteCode } = body;

    if (!credential) {
      return json({ success: false, error: "Missing Google ID token" }, { status: 400 });
    }

    // Handle Google authentication
    const result = await handleGoogleAuth(credential, request, context.cloudflare?.env);

    if (!result.success) {
      return json({ success: false, error: result.error }, { status: 401 });
    }

    // Set authentication cookies
    const cookieHeaders = setAuthCookies(result.accessToken!, result.refreshToken!);

    // Return success response with user data
    return json(
      {
        success: true,
        user: result.user,
        message: "Authentication successful",
      },
      {
        headers: cookieHeaders,
      }
    );
  } catch (error) {
    console.error("Google auth route error:", error);

    return json(
      {
        success: false,
        error: "Internal server error",
      },
      { status: 500 }
    );
  }
}

// Loader to handle GET requests (redirect to sign in)
export async function loader() {
  return new Response(null, {
    status: 302,
    headers: {
      Location: "/auth/signin",
    },
  });
}
